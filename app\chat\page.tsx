"use client";
import { AIInputWithLoading } from "@/components/ui/ai-input-with-loading";
import React, { useState, useEffect } from "react";
import { useSession } from "@/hooks/use-session";
import { useChatContext } from "@/contexts/ChatContext";
import { useStreamingChat } from "@/hooks/use-streaming-chat";
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import logo from '@/assets/chattylogo.svg';

function ChatPage() {
  const { session } = useSession();
  const { state } = useChatContext();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { sendMessage, isStreaming } = useStreamingChat({
    onStreamStart: (chatId) => {
      console.log('Stream started for chat:', chatId);
      // Navigate to the new chat without page refresh
      router.push(`/chat/${chatId}`);
    },
    onStreamChunk: (content) => {
      // Content is automatically handled by the chat context
      console.log('Received chunk:', content);
    },
    onStreamComplete: (fullContent) => {
      console.log('Stream completed:', fullContent);
      setIsSubmitting(false);
    },
    onStreamError: (error) => {
      console.error('Streaming error:', error);
      setIsSubmitting(false);
    }
  });

  const handleMessageSubmit = async (message: string) => {
    if (!session.user?.email) {
      console.error("User not authenticated");
      return;
    }

    if (isStreaming || isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      const result = await sendMessage(message, undefined, session.user.email);

      if (!result.success) {
        console.error("Message submission failed:", result.error);
        setIsSubmitting(false);
      }
      // Success handling is done in the streaming callbacks
    } catch (error) {
      console.error("Message submission failed:", error);
      setIsSubmitting(false);
    }
  };

  // Show welcome screen when no current chat and no messages
  const showWelcome = !state.currentChatId && Object.keys(state.messages).length === 0;
  const currentMessages = state.currentChatId ? state.messages[state.currentChatId] || [] : [];

  return (
    <>
      {showWelcome && !isSubmitting && !isStreaming && (
        <div className="flex flex-col items-center space-y-4 justify-center h-full">
          <Image
            src={logo}
            width={500}
            height={500}
            alt="Picture of the author"
          />
          <h1 className="text-[2rem]">Start a new Chat</h1>
          <h1 className="text-[1.5rem] pb-2 text-gray-400">Chat with our AI</h1>
        </div>
      )}

      <div className="space-y-8 min-w-[350px] flex flex-col items-center">
        <div className="space-y-4 w-[60%] absolute bottom-0">
          {currentMessages.map((msg) => (
            <div
              key={msg.id}
              className={`p-4 rounded-lg w-max min-w-[20%] max-w-[70%] ${
                msg.role === "user"
                  ? "ml-auto bg-black/5 dark:bg-white/5"
                  : "mr-auto bg-blue-500/10"
              }`}
            >
              <h1 className="text-lg font-semibold pb-2">
                {msg.role === "user" ? "You" : "AI"}
              </h1>
              <div className="whitespace-pre-wrap">{msg.content}</div>
            </div>
          ))}

          <AIInputWithLoading
            onSubmit={handleMessageSubmit}
            loadingDuration={3000}
            placeholder="Type a message..."
            disabled={isSubmitting || isStreaming}
          />
        </div>
      </div>
    </>
  );
}

export default ChatPage;