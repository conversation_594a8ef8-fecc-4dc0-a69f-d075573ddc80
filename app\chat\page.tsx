"use client";
import { AIInputWithLoading } from "@/components/ui/ai-input-with-loading";
import React, { useState } from "react";
import { createMessage } from "@/actions/chat-actions";
import { useSession } from "@/hooks/use-session";
import Image from 'next/image'
import logo from '@/assets/chattylogo.svg'
import { useRouter } from 'next/navigation'

interface Message {
  content: string;
  role: "user" | "assistant";
}

function ChatPage() {
  const { session } = useSession();
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatId, setChatId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const handleMessageSubmit = async (message: string) => {
    if (!session.user?.email) {
      console.error("User not authenticated");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await createMessage({
        userEmail: session.user.email,
        content: message,
        currentChatId: chatId
      });

      setMessages(prev => [
        ...prev,
        { content: message, role: "user" },
        { content: result.aiResponse.message.content, role: "assistant" }
      ]);

      if (!chatId) setChatId(result.chatId);
      router.push(`/chat/${result.chatId}`);
    } catch (error) {
      console.error("Message submission failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {messages.length === 0 && !isSubmitting && (
        <div className="flex flex-col items-center space-y-4 justify-center h-full">
          <Image
            src={logo}
            width={500}
            height={500}
            alt="Picture of the author"
          />
          <h1 className="text-[2rem]">Start a new Chat</h1>
          <h1 className="text-[1.5rem] pb-2 text-gray-400">Chat with our AI</h1>
        </div>
      )}

      <div className="space-y-8 min-w-[350px] flex flex-col items-center">
        <div className="space-y-4 w-[60%] absolute bottom-0">
          {messages.map((msg, i) => (
            <div
              key={i}
              className={`p-4 rounded-lg w-max min-w-[20%] max-w-[70%] ${msg.role === "user"
                ? "ml-auto bg-black/5 dark:bg-white/5"
                : "mr-auto bg-blue-500/10"
                }`}
            >
              <h1 className="text-lg font-semibold pb-2">
                {msg.role === "user" ? "You" : "AI"}
              </h1>
              {msg.content}
            </div>
          ))}

          <AIInputWithLoading
            onSubmit={handleMessageSubmit}
            loadingDuration={3000}
            placeholder="Type a message..."
          />
        </div>
      </div>
    </>
  );
}

export default ChatPage;