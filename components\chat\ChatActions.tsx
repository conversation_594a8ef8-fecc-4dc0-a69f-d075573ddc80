"use client";

import { deleteChat } from "@/actions/chat-actions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { SidebarMenuAction } from "../ui/sidebar";
import { ShareChatForm } from "./ShareChatForm";

export function ChatActions({ chatId }: { chatId: string }) {
  const router = useRouter();

  const handleDelete = async () => {
    try {
      await deleteChat(chatId);
      toast.success("Chat deleted successfully");
      router.refresh(); // Refresh the page to reflect changes
    } catch (error) {
      toast.error("Failed to delete chat");
      console.error(error);
    }
  };


  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuAction>
          <MoreHorizontal />
        </SidebarMenuAction>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="right" align="start">
          <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
            <ShareChatForm chatId={chatId} />
          </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDelete}>
          <span>Delete Chat</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}