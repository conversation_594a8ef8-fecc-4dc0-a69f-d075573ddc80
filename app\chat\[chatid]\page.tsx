"use client";
import { AIInputWithLoading } from "@/components/ui/ai-input-with-loading";
import React, { useState, useEffect, useRef, useCallback } from "react";
import { createMessage, getChatMessages } from "@/actions/chat-actions";
import { useSession } from "@/hooks/use-session";
import { Message } from "@/types";

function ChatPage({ params }: { params: { chatid: string } }) {
  const { session } = useSession();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const initialized = useRef(false);

  const loadMessages = useCallback(async () => {
    try {
      const chatMessages = await getChatMessages(params.chatid);
      setMessages(chatMessages.map(msg => ({
        id: Date.now().toString(), // Generate a unique ID
        role: msg.type === "USER" ? "user" : "assistant", // Map type to role
        content: msg.content,
        sender: msg.sender
          ? {
              id: msg.sender.id,
              role: msg.sender.role === "USER" ? "USER" : "ADMIN",
              name: msg.sender.name || undefined,
              email: msg.sender.email || undefined
            }
          : undefined,
        createdAt: msg.createdAt
      })));
    } catch (error) {
      console.error("Failed to load chat:", error);
    }
  }, [params.chatid]);

  useEffect(() => {
    if (!initialized.current && session?.user?.email) {
      initialized.current = true;
      loadMessages();
    }
  }, [session, loadMessages]); // Added loadMessages to dependency array

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleMessageSubmit = async (message: string) => {
    if (!session?.user?.email) return;

    setIsSubmitting(true);
    
    // Optimistic update for user message
    const tempId = Date.now().toString();
    setMessages(prev => [
      ...prev,
      {
        content: message,
        role: "user",
        id: tempId,
        sender: { id: session.user.id, role: "USER", email: session.user.email }
      }
    ]);

    try {
      const result = await createMessage({
        userEmail: session.user.email,
        content: message,
        currentChatId: params.chatid
      });

      // Add AI response while keeping user message
      setMessages(prev => [
        ...prev.filter(msg => msg.id !== tempId), // Remove temporary message
        {
          id: tempId, // Add a unique ID for the user message
          content: message,
          role: "user",
          sender: { id: session.user.id, role: "USER" }
        },
        {
          id: Date.now().toString(), // Add a unique ID for the AI response
          content: result.aiResponse.message.content,
          role: "assistant",
          sender: { id: "AI", role: "ADMIN" }
        }
      ]);

      // Alternatively, refresh messages from server
      await loadMessages();
      
    } catch (error) {
      setMessages(prev => prev.filter(msg => msg.id !== tempId));
      console.error("Message submission failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col h-[94%]">
      {messages.length === 0 && !isSubmitting && (
        <div className="flex flex-col items-center space-y-4 justify-center flex-grow">
          <h1 className="text-[2rem]">Loading...</h1>
        </div>
      )}

      <div className="flex flex-col h-full overflow-y-hidden">
        <div className="flex-grow overflow-y-auto space-y-4">
          <div className="w-full max-w-3xl mx-auto space-y-4">
            {messages.map((msg) => (
              <div
                key={msg.id}
                className={`p-4 rounded-lg w-max min-w-[20%] max-w-[70%] ${
                  msg.sender?.role === "USER" 
                    ? "ml-auto bg-black/5 dark:bg-white/5"
                    : "mr-auto bg-blue-500/10"
                }`}
              >
                {msg.sender?.role === 'ADMIN' && (
                  <h1 className="text-lg font-semibold pb-2">AI Assistant</h1>
                )}
                {msg.sender?.role === 'USER' && (
                  <h1 className="text-lg font-semibold pb-2">
                    {msg.sender?.name || 'You'}
                  </h1>
                )}
                <div className="whitespace-pre-wrap">{msg.content}</div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </div>

        <div className="p-2 border-t">
          <div className="max-w-3xl mx-auto">
            <AIInputWithLoading
              onSubmit={handleMessageSubmit}
              loadingDuration={3000}
              placeholder="Type a message..."
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default ChatPage;