import { auth } from "@/auth";
import ollama from 'ollama';
import prisma from "@/prisma";
import { NextRequest } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { message, chatId } = await req.json();

    if (!message || typeof message !== 'string') {
      return new Response('Invalid message', { status: 400 });
    }

    // Get user and AI user
    const [user, aiUser] = await Promise.all([
      prisma.user.findUnique({ where: { email: session.user.email } }),
      prisma.user.findUnique({ where: { email: '<EMAIL>' } })
    ]);

    if (!user || !aiUser) {
      return new Response('User configuration error', { status: 500 });
    }

    // Handle chat creation or validation
    let chat;
    if (chatId) {
      // Validate existing chat
      chat = await prisma.chat.findFirst({
        where: {
          id: chatId,
          OR: [
            { userId: user.id }, // Owner
            { shares: { some: { recipientId: user.id } } } // Shared with user
          ]
        }
      });
      
      if (!chat) {
        return new Response('Chat not found or access denied', { status: 404 });
      }
    } else {
      // Create new chat
      chat = await prisma.chat.create({
        data: {
          title: message.substring(0, 50),
          userId: user.id,
        },
      });
    }

    // Save user message to database
    await prisma.message.create({
      data: {
        content: message,
        type: "USER",
        chatId: chat.id,
        senderId: user.id,
      },
    });

    // Fetch previous messages for context
    const previousMessages = await prisma.message.findMany({
      where: { chatId: chat.id },
      orderBy: { createdAt: "asc" },
      select: { content: true, type: true }
    });

    // Format messages for Ollama API
    const formattedMessages = previousMessages.map((msg) => ({
      role: msg.type === "USER" ? "user" : "assistant",
      content: msg.content
    }));

    // Create a readable stream for Server-Sent Events
    const encoder = new TextEncoder();
    
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Send initial metadata
          const initialData = {
            type: 'metadata',
            chatId: chat.id,
            timestamp: new Date().toISOString()
          };
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(initialData)}\n\n`)
          );

          let fullResponse = '';
          
          // Stream response from Ollama
          const response = await ollama.chat({
            model: 'gemma3:4b',
            messages: formattedMessages,
            stream: true,
          });

          // Process the streaming response
          for await (const part of response) {
            if (part.message?.content) {
              fullResponse += part.message.content;
              
              // Send streaming chunk
              const chunkData = {
                type: 'chunk',
                content: part.message.content,
                timestamp: new Date().toISOString()
              };
              
              controller.enqueue(
                encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`)
              );
            }
          }

          // Save complete AI response to database
          await prisma.message.create({
            data: {
              content: fullResponse,
              type: "ASSISTANT",
              chatId: chat.id,
              senderId: aiUser.id,
            },
          });

          // Send completion signal
          const completionData = {
            type: 'complete',
            fullContent: fullResponse,
            timestamp: new Date().toISOString()
          };
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(completionData)}\n\n`)
          );

          // Close the stream
          controller.close();
          
        } catch (error) {
          console.error('Streaming error:', error);
          
          // Send error to client
          const errorData = {
            type: 'error',
            message: 'An error occurred while generating the response',
            timestamp: new Date().toISOString()
          };
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`)
          );
          
          controller.close();
        }
      },
    });

    // Return the stream with appropriate headers for Server-Sent Events
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('API Error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
